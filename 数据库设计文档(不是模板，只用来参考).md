# ***\*1\*******\*．需求分析\****

## ***\*1.1\**** ***\*需求描述\****

1. 运动会主界面设置密码，用户通过密码后才能进入系统的水平命令菜单；水平命令菜单主要包括：运动员管理，后勤管理，各系管理，运动会组委会管理等模块
2. 运动员管理模块主要包括：添加运动员信息，查询全系运动员信息，查看赛程信息，查看比赛得分等菜单项。
3. 后勤管理模块主要包括：场地安排，运动员体检，安全保卫，车辆安排等菜单项。
4. 各系管理模块主要包括：添加运动员，教练安排，运动会报名，修改运动员信息，查询赛程安排，查询排名信息等菜单项。
5. 运动会组委会模块主要包括：管理员，数据录入，宣传等菜单项。

##  ***\*1.\*******\*2\**** ***\*功能\*******\*需求\****

本系统包括教学管理及财务管理两个子系统，共有运动员管理、各系负责人、后勤部门、运动会组委会，四个功能模块。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps2.png)

**图1-1  总体功能模块图**

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps3.png)

**图1-2  运动员管理模块**

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps4.png)

**图1-3  各系管理模块**

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps5.png)

**图1-4  后勤部门管理模块**

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps6.png)

**图1-5  运动会组委会管理模块**

 

## ***\*1.\*******\*3\**** ***\*功能描述\****

1．运动员管理：本功能模块提供了运动员登录信息，填写信息，查询信息，查询赛程，查询成绩等功能。

2．各系负责人：该功能模块提供了各系报名，分配学生学号，修改信息，查询全系赛程场地安排，查询全系排名和教练安排等功能。 

3．后勤部门：可以管理运动员休息，比赛场地，观众席的安排。其中的医疗部负责体检记录和伤病记录。安全部负责赛场秩序和人员安全。还有安排运动会期间的车辆部署。

4．运动会组委会：包括管理员，数据导入，和宣传组，其中管理员管理所有的账号和赛程安排。数据导入负责导入比赛成绩。宣传组负责开幕式，宣传和颁奖。

## ***\*1\*******\*.\*******\*4\**** ***\*信息需求\****

通过分析，我们可以得到本系统中主要管理的对象是运动员、各系、赛程、比赛项目，需存储的信息包括： 

● 运动员信息：具有学号、姓名、性别位置等信息。

● 教练：具有编号、姓名、等级信息。

● 系：具有编号、系别、领导信息。

● 参赛者：具有名称、类别等信息。

● 裁判：编号、姓名、等级信息。

● 赛程：编号、地点、时间信息。

● 比赛项目：编号、名称、人数等信息。

通过分析，本系统中各实体之间的联系如下：

● 系-运动员：一个运动员只能在一个系，一个系可以有多个运动员，因此系和运动员之间的关系是一对多的关系。

● 运动员-教练：一个运动员可以在多个教练那训练，一个教练可以训练多个运动员，因此运动员和教练之间是多对多的关系。

●比赛项目-赛程：一个比赛项目属于一个赛程，一个赛程可以有多个比赛项目，因此比赛项目和赛程之间是一对多的关系。

●赛程-参赛者：一个参赛者可以有多个参赛者，一个参赛者也可以有多个赛程，因此赛程和参赛者之间是多对多的关系。

●赛程-裁判：一个赛程可以有多个裁判，一个裁判可以评判多个赛程，因此赛程和裁判之间是多对多关系。

●赛程-成绩-参赛者：一个赛程可以为有多个参赛者的多个成绩，一个参赛者可以在多个赛程中得到多个成绩，一个成绩可以被多个参赛者在多个赛程中获得。

# ***\*2\*******\*．概念结构设计\****

## ***\*2.1\**** ***\*局部\*******\*E-R\*******\*图的设计\****

1）运动员与系的E-R图

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps7.png) 

**图2-1**  运动员与系的E-R图

2）赛程和参赛者的E-R图

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps8.png) 

**图2-2**  赛程和参赛者E-R图

3）比赛项目、赛程、参赛者和成绩的E-R图

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps9.png) 

**图2-3**  比赛项目、赛程、参赛者和成绩的E-R图

 

## ***\*2.2\**** ***\*全局\*******\*E-R\*******\*图\*******\*的设计\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps10.png) 

**图2-4**  运动会管理系统全局E-R图

# ***\*3\*******\*．逻辑结构设计\****

## ***\*3\*******\*.\*******\*1\**** ***\*关系模式设计\****

根据E-R图中的实体及实体之间的联系，关系模式如下：

● 运动员信息（学号，姓名，性别，系别，年龄）

● 教练（编号，姓名，等级）

● 系（编号，系名，领导）

● 参赛者（编号，名称，类别，组员）

● 赛程（编号，地点，时间）

● 比赛项目（编号，名称，人数，类别）

● 裁判（编号，姓名，等级）

● 成绩（编号，分数，类别，单位，是否破记录）

● 运动员-系（编号，运动员编号，系编号）

**注：参考内容截止到此，后半部分的内容格式按《数据库设计文档》完成。**

\---------------------------------------------------------------------

## ***\*3\*******\*.\*******\*2\**** ***\*关系表\****

将前面得到的关系模式转换为SQL Server2005支持的具体关系表如下：

1) ath_coach_dept：运动员-教练-系关系表

| **字段名**                | **字段类型** | **字段宽度** | **是否允许空** | **说明**                         |
| ------------------------- | ------------ | ------------ | -------------- | -------------------------------- |
| **ath_coach_dept****_id** | **INT**      | **4**        | **NOT NULL**   | **运动员教练系关系表编号，主键** |
| **athlet_information**    | **INT**      | **4**        | **NULL**       | **运动员信息**                   |
| **coach_id**              | **INT**      | **4**        | **NULL**       | **教练编号**                     |
| **dept_id**               | **INT**      | **4**        | **NULL**       | **系编号**                       |

 

2) ath_dept运动员-系关系表

| **字段名**                        | **字段类型** | **字段宽度** | **是否允许空** | **说明**                     |
| --------------------------------- | ------------ | ------------ | -------------- | ---------------------------- |
| **A****th_dept****_id**           | **INT**      | **4**        | **NOT NULL**   | **运动员系关系表编号，主键** |
| **A****thlet_information_number** | **INT**      | **4**        | **NULL**       | **运动员信息编号**           |
| **dept_id**                       | **INT**      | **4**        | **NULL**       | **系编号**                   |

 

3) Athlet_information运动员信息表

| **字段名**  | **字段类型** | **字段宽度** | **是否允许空** | **说明**             |
| ----------- | ------------ | ------------ | -------------- | -------------------- |
| **number**  | **VARCHAR**  | **20**       | **NOT NULL**   | **运动员信息，主键** |
| **name**    | **VARCHAR**  | **20**       | **NULL**       | **运动员姓名**       |
| **sex**     | **INT**      | **4**        | **NULL**       | **运动员性别**       |
| **age**     | **INT**      | **4**        | **NULL**       | **运动员年龄**       |
| **dept**    | **VARCHAR**  | **20**       | **NULL**       | **运动员所在系别**   |
| **healthy** | **INT**      | **4**        | **NULL**       | **运动员是否健康**   |

 

4) Coach教练表

| **字段名**      | **字段类型** | **字段宽度** | **是否允许空** | **说明**           |
| --------------- | ------------ | ------------ | -------------- | ------------------ |
| **coach_id**    | **INT**      | **4**        | **NOT NULL**   | **教练编号，主键** |
| **coach_name**  | **VARCHAR**  | **16**       | **NULL**       | **教练姓名**       |
| **coach_level** | **INT**      | **4**        | **NULL**       | **教练级别**       |

 

5) competer参赛者表

| **字段名**                    | **字段类型** | **字段宽度** | **是否允许空** | **说明**             |
| ----------------------------- | ------------ | ------------ | -------------- | -------------------- |
| **competer****_id**           | **INT**      | **4**        | **NOT NULL**   | **参赛者编号，主键** |
| **competer****_****namr**     | **VARCHAR**  | **16**       | **NULL**       | **参赛者姓名**       |
| **competer****_****category** | **VARCHAR**  | **16**       | **NULL**       | **参赛者类别**       |
| **competer****_****member**   | **VARCHAR**  | **16**       | **NULL**       | **参赛者组员**       |

 

6) competer_race_grade参赛者-赛程-成绩关系表

| **字段名**                       | **字段类型** | **字段宽度** | **是否允许空** | **说明**                       |
| -------------------------------- | ------------ | ------------ | -------------- | ------------------------------ |
| **competer****_****race_****id** | **INT**      | **4**        | **NOT NULL**   | **参赛者赛程关系表编号，主键** |
| **competer****_****id**          | **INT**      | **4**        | **NULL**       | **参赛者编号**                 |
| **race_id**                      | **INT**      | **4**        | **NULL**       | **赛程编号**                   |
| **grade_id**                     | **INT**      | **4**        | **NULL**       | **成绩编号**                   |

 

7) dept系表

| **字段名**            | **字段类型** | **字段宽度** | **是否允许空** | **说明**         |
| --------------------- | ------------ | ------------ | -------------- | ---------------- |
| **dept****_id**       | **INT**      | **4**        | **NOT NULL**   | **系编号，主键** |
| **dept****_****namr** | **VARCHAR**  | **16**       | **NULL**       | **系名**         |
| **leader**            | **VARCHAR**  | **16**       | **NULL**       | **系领导**       |

 

8) doc_ath医生-运动员关系表

| **字段名**                  | **字段类型** | **字段宽度** | **是否允许空** | **说明**                       |
| --------------------------- | ------------ | ------------ | -------------- | ------------------------------ |
| **doc****_id**              | **INT**      | **4**        | **NOT NULL**   | **医生运动员关系表编号，主键** |
| **D****octor_d_id**         | **INT**      | **4**        | **NULL**       | **医生编号**                   |
| **athlet_informent_number** | **INT**      | **4**        | **NULL**       | **运动员编号**                 |

 

9) doc_race医生-赛程关系表

| **字段名**      | **字段类型** | **字段宽度** | **是否允许空** | **说明**                     |
| --------------- | ------------ | ------------ | -------------- | ---------------------------- |
| **doc_race_id** | **INT**      | **4**        | **NOT NULL**   | **医生赛程关系表编号，主键** |
| **d_id**        | **INT**      | **4**        | **NULL**       | **医生编号**                 |
| **race_id**     | **INT**      | **4**        | **NULL**       | **赛程编号**                 |

 

10) doctor医生表

| **字段名**     | **字段类型** | **字段宽度** | **是否允许空** | **说明**     |
| -------------- | ------------ | ------------ | -------------- | ------------ |
| **d_id**       | **INT**      | **4**        | **NOT NULL**   | **医生编号** |
| **d_name**     | **VARCHAR**  | **16**       | **NULL**       | **医生姓名** |
| **d_category** | **VARCHAR**  | **16**       | **NULL**       | **医生类别** |

 

11) grade成绩表

| **字段名**         | **字段类型** | **字段宽度** | **是否允许空** | **说明**           |
| ------------------ | ------------ | ------------ | -------------- | ------------------ |
| **grade_id**       | **INT**      | **4**        | **NOT NULL**   | **成绩编号，主键** |
| **grade_score**    | **FLOAT**    | **8**        | **NULL**       | **分数**           |
| **grade_categroy** | **VARCHAR**  | **16**       | **NULL**       | **分数类别**       |
| **grade_unit**     | **VARCHAR**  | **16**       | **NULL**       | **成绩计量单位**   |
| **grade_record**   | **INT**      | **4**        | **NULL**       | **成绩是否破纪录** |

 

12) judjment裁判表

| **字段名**         | **字段类型** | **字段宽度** | **是否允许空** | **说明**     |
| ------------------ | ------------ | ------------ | -------------- | ------------ |
| **judjment_id**    | **INT**      | **4**        | **NOT NULL**   | **裁判编号** |
| **judjment_name**  | **VARCHAR**  | **16**       | **NULL**       | **裁判姓名** |
| **judjment_level** | **VARCHAR**  | **16**       | **NULL**       | **裁判级别** |

 

13) login登录表

| **字段名**   | **字段类型** | **字段宽度** | **是否允许空** | **说明**         |
| ------------ | ------------ | ------------ | -------------- | ---------------- |
| **id**       | **VARCHAR**  | **20**       | **NOT NULL**   | **登陆ID，主键** |
| **name**     | **VARCHAR**  | **20**       | **NULL**       | **用户名**       |
| **password** | **VARCHAR**  | **20**       | **NULL**       | **密码**         |
| **category** | **VARCHAR**  | **20**       | **NULL**       | **类别**         |
| **lock**     | **INT**      | **4**        | **NULL**       | **是否锁定**     |

 

14) program比赛项目表

| **字段名**           | **字段类型** | **字段宽度** | **是否允许空** | **说明**               |
| -------------------- | ------------ | ------------ | -------------- | ---------------------- |
| **program_id**       | **INT**      | **4**        | **NOT NULL**   | **比赛项目编号，主键** |
| **program_name**     | **VARCHAR**  | **20**       | **NULL**       | **比赛项目名称**       |
| **program_number**   | **INT**      | **4**        | **NULL**       | **比赛项目人数**       |
| **program_categroy** | **VARCHAR**  | **20**       | **NULL**       | **比赛项目类别**       |

 

15) program_race比赛项目-赛程关系表

| **字段名**          | **字段类型** | **字段宽度** | **是否允许空** | **说明**                           |
| ------------------- | ------------ | ------------ | -------------- | ---------------------------------- |
| **program_race_id** | **INT**      | **4**        | **NOT NULL**   | **比赛项目和赛程关系表编号，主键** |
| **program_id**      | **INT**      | **4**        | **NULL**       | **比赛项目编号**                   |
| **race_id**         | **INT**      | **4**        | **NULL**       | **赛程编号**                       |

 

16) race赛程表

| **字段名**          | **字段类型** | **字段宽度** | **是否允许空** | **说明**       |
| ------------------- | ------------ | ------------ | -------------- | -------------- |
| **r****a****ce_id** | **INT**      | **4**        | **NOT NULL**   | **赛程编号**   |
| **race_place**      | **VARCHAR**  | **16**       | **NULL**       | **比赛地点**   |
| **race_time**       | **DATETIME** | **8**        | **NULL**       | **比赛时间**   |
| **race_leader**     | **VARCHAR**  | **16**       | **NULL**       | **比赛领导者** |

 

17) race_judjement赛程-裁判关系表

| **字段名**           | **字段类型** | **字段宽度** | **是否允许空** | **说明**                     |
| -------------------- | ------------ | ------------ | -------------- | ---------------------------- |
| **race_judjment_id** | **INT**      | **4**        | **NOT NULL**   | **赛程裁判关系表编号，主键** |
| **race_id**          | **INT**      | **4**        | **NULL**       | **赛程编号**                 |
| **judjment_id**      | **INT**      | **4**        | **NULL**       | **裁判编号**                 |

 

## ***\*3\*******\*.\*******\*3\**** ***\*数据库实施代码\****

### ***\*3\*******\*.\*******\*3\*******\*.1\**** ***\*创建数据库\****

USE master

GO

CREATE DATABASE  SMMS

ON 

( NAME = SMMS

  FILENAME = 'd:\smms.mdf',

  SIZE = 10,

  MAXSIZE = 50,

  FILEGROWTH = 5 )

LOG ON

( NAME = SMMS ,

  FILENAME = 'd:\smms.ldf',

  SIZE = 5MB,

  MAXSIZE = 25MB,

  FILEGROWTH = 5MB )

GO

 

### ***\*3\*******\*.\*******\*3\*******\*.\*******\*2\**** ***\*创建\*******\*关系表\****

**1）创建****各系信息表**

create table dept (

  dept_id        int(4)         not null,

  dept_name      varchar(20)         null,

  leader        varchar(20)         null,

  constraint PK_DEPT primary key nonclustered (dept_id)

)

Go

**2）创建****运动员信息表**

create table athlet_information (

  number        varchar(20)         not null,

  name         varchar(20)         null,

  sex          int(4)           null,

  age          int(4)           null,

  dept          varchar(20)         null,

  healthy         int(4)           null,

  constraint PK_ATHLET_INFORMNTION primary key nonclustered (number)

)

Go

**3）创建****成绩表**

create table grade (

  grade_id       int(4)         not null,

  grade_score      float(0)         null,

  grade_categroy    varchar(20)       null,

  grade_unit      varchar(20)       null,

  grade_record     int(4)          null,

  constraint PK_GRADE primary key nonclustered (grade_id)

)

Go

**4）创建****赛程表**

create table race (

  rce_id        int(4)         not null,

  race_place       varchar(20)       null,

  race_time       datetime         null,

  race_leader      varchar(20)       null,

  constraint PK_RACE primary key nonclustered (rce_id)

)

Go

**5）****创建比赛项目表**

create table program (

  program_id      int(4)         not null,

  program_name     varchar(20)      null,

  program_content    varchar(20)      null,

  program_categroy    varchar(20)      null,

  constraint PK_PROGRAM primary key nonclustered (program_id)

)

Go

**6）****创建赛程—比赛项目表**

create table program_race (

  program_race_id    int(4)         not null,

  program_id      int(4)         null,

  race_id        int(4)          null,

  constraint PK_PROGRAM_RACE primary key nonclustered (program_race_id)

)

Go

**7）****创建赛程—运动员—成绩表**

create table athletes_race_grade (

  competer_race_id   int(4)         not null,

  competer_id      int(4)         null,

  race_id        int(4)         null,

  grade_id       int(4)         null,

  constraint PK_COMPETER_RACE_GRADE primary key nonclustered (competer_race_id)

)

Go

 

**8）****创建登录表**

create table login (

  id          int(4)         not null,

  name         varchar(20)       null,

  password       varchar(20)       null,

  category        varchar(20)       null,

  lock          int(4)         null,

  constraint PK_LOGIN primary key nonclustered (id)

)

Go

**9）****创建医生表**

create table doctor (

  d_id          int(4)         not null,

  d_name        varchar(20)      null,

  d_category       varchar(20)      null,

  constraint PK_DOCTOR primary key nonclustered (d_id)

)

Go

**9）****创建医生—赛程表**

create table doc_race (

  doc_race_id      int(4)         not null,

  d_id         int(4)         null,

  race_id        int(4)         null,

  constraint PK_DOC_RACE primary key nonclustered (doc_race_id)

)

Go

**10）****教练信息系表**

create table coach (

  coach_id       int(4)         not null,

  coach_name      varchar(20)      null,

  coach_level      int(4)         null,

  constraint PK_COACH primary key nonclustered (coach_id)

)

go

**11）****运动员—教练—系表**

create table ath_coach_dept (

  ath_coach_dept_id     int(4)         not null,

  athlet_informntin_number int(4)         null,

  coach_id         int(4)         null,

  Attribute_43       int(4)         null,

  constraint PK_ATH_COACH_DEPT primary key nonclustered (ath_coach_dept_id)

)

Go

**12）****裁判信息表**

create table judjment (

  judjment_id      int(4)         not null,

  judjment_name    int(4)         null,

  judjment_level    int(4)         null,

  constraint PK_JUDJMENT primary key nonclustered (judjment_id)

)

Go

**13）****裁判—赛程表**

create table race_judjemrnt (

  race_judjment_id   int(4)         not null,

  race_id       int(4)         null,

  judjment_id     int(4)          null,

  constraint PK_RACE_JUDJEMRNT primary key nonclustered (race_judjment_id)

)

Go

**14）****G医生信息表**

create table doctor (

  d_id         int(4)           not null,

  d_name       varchar(20)         null,

  d_category      varchar(20)         null,

  constraint PK_DOCTOR primary key nonclustered (d_id)

)

Go

**15）****G医生—赛程表**

create table doctor (

  d_id         int(4)           not null,

  d_name       varchar(20)         null,

  d_category      varchar(20)         null,

  constraint PK_DOCTOR primary key nonclustered (d_id)

)

Go

**16）****运动员—系表**

create table ath_dept (

  ath_dept_id        int(4)          not null,

  athlet_informent_number  int(4)          null,

  dept_id         int(4)          null,

  constraint PK_ATH_DEPT primary key nonclustered (ath_dept_id)

)

Go

**16）****系医生—运动员表**

create table doc_ath (

  da_id          int(4)         not null,

  doctor_d_id       int(4)         null,

  athlet_informent_number  int(4)          null,

  constraint PK_DOC_ATH primary key nonclustered (da_id)

)

Go

### ***\*3\*******\*.\*******\*3\*******\*.\*******\*3\**** ***\*数据初始化\****

**1）****将用户信息输入到班级****表**

insert into login  values('200796014093','王磊','123',’运动员’,’0’) 

insert into login  values('jsjkxx','计算机科学系','123',’系管理员’,’0’) 

insert into login  values('admin','管理员','123',’管理员’,’0’)

insert into login  values('doctor','杜小奇','123',’医生’,’0’)

 

**2）****将幼儿档案信息输入到幼儿表**

insert into  ath_information  values(‘200796014093’,’王磊’,1,20,’CS’,’健康’) 

insert into  ath_information  values(‘200796014090’,’杜小奇’,1,20,’CS’,’健康’) 

insert into  ath_information  values(‘20099601S024’,’孙青’,0,20,’CS’,’健康’) 

insert into  ath_information  values(‘20096014S036’,’薛玲’,0,20,’CS’,’健康’) 

 

**3）****将赛程信息输入到赛程表**

insert into  ath_information  values(‘1000米跑’,’ 体育场跑道’,’ 2010-4-22 8:00:00’,’王磊’) 

insert into  ath_information  values(‘100米跑’,’ 体育场跑道’,’ 2010-4-22 9:00:00’,’ 杜小奇’) 

insert into  ath_information  values(‘100米游泳’,’ 体育馆’,’ 2010-4-22 10:00:00’,’ 孙青’) 

insert into  ath_information  values(‘100米射击’,’ 体育馆1号射击场’,’ 2010-4-22 11:00:00’,’ 薛玲’) 

 