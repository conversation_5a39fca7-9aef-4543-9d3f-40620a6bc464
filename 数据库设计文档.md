××系统数据库设计文档

一、需求分析

标准版的内容结构：

1.1 需求描述

1.2 功能需求

1.3 功能描述

1.4 信息需求

简化版的内容结构（推荐）：

1.1 功能模块划分及介绍

1.2 实体及重要属性

1.3 业务流程图

二、概念结构设计

2.1 局部E-R图的设计

说明：可省略。 

2.2 全局E-R图的设计

注意：要和简化版1.1及1.2的内容相对应，只需画出新增属性。

三、逻辑结构设计

3.1 关系模式设计

注意：要和ER图相对应，把属性补充完整，不必标出主外键。

3.2 表设计

3.2.1 表结构

说明：根据关系模式设计表结构。

表1  zgxx（职工信息表）

| **字段名** | **描述**  | **数据类型**   | **可否为空** | **备注**                     |
| ---------- | --------- | -------------- | ------------ | ---------------------------- |
| Eid        | 职工号    | Char（10）     | 否           | 主键                         |
| Name       | 职工姓名  | Varchar（10）  | 否           |                              |
| Birthday   | 出生日期  | Datetime       | 是           |                              |
| Sex        | 性别      | Char（2）      | 是           | (男，女)                     |
| Party      | 政治面貌  | Varchar（20）  | 是           | (党员，团员，民主党派，群众) |
| Post       | 岗位/职务 | Vharchar（10） | 是           |                              |
| Wid        | 车间号    | Char（2）      | 否           | 外键（对应于车间表的Wid）    |

3.2.2 数据库关系图

说明：根据3.2.1设计的表结构画出表间关系图。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml19044\wps1.jpg)

四、物理设计

4.1 数据库的存放位置和存储结构

4.1.1 数据库的存放位置

4.1.2 数据库的存储结构

说明：写出数据库分为几个文件组，每个文件组中有什么文件，表存放于那个文

件组中。

4.2 索引设计

说明：每张表的索引设置。

表2  索引表

| **表名**           | **建立索引的字段** | **类型** | **列值是否唯一** |
| ------------------ | ------------------ | -------- | ---------------- |
| zgxx（职工信息表） | Eid（职工号）      | 聚集索引 | 是               |
| Name（职工姓名）   | 非聚集索引         | 否       |                  |
|                    |                    |          |                  |
|                    |                    |          |                  |
|                    |                    |          |                  |
|                    |                    |          |                  |
|                    |                    |          |                  |

五、数据库实施

5.1 创建数据库

说明：创建数据库的代码，和4.1的内容一致。

5.2 创建表

说明：创建表的代码，和4.1、5.3.1的内容一致。

5.3 完整性设计

5.3.1 字段的约束

说明：此处不必写出，在表结构的备注信息里进行说明；写在创建表的代码中，和表结构的备注内容一致。

1.主键

2.外键

3.对表中某一字段取值的约束，如：性别只能填入（男，女）。

5.3.2 存储过程

说明：可省略。

表3  存储过程描述表

| **存储过程编号：01**                                         |
| ------------------------------------------------------------ |
| 存储过程英文名称：GetCategories                              |
| 存储过程中文名称：获取商品类别                               |
| 存储过程内容：creat  procedure[dbo].[ GetCategories] as  Begin      Set  nocount  on;      Select[name] from [Categories]  order  by [ID]  end |
| 说明：此存储过程用于返回所有的商品类别                       |

5.3.3 触发器

说明：可省略。

5.4 安全性设计

说明：可省略。

5.4.1 账号和密码

5.4.2 角色与权限

说明：确定每个角色对数据库表的操作权限，如创建、检索、更新、删除等。每个角色拥有刚好能够完成任务的权限，不多也不少。在应用时再为用户分配角色，则每个用户的权限等于他所兼角色的权限之和。

 

表4  角色、权限分配表

| **角色**     | **可以访问的表与列** | **操作权限** |
| ------------ | -------------------- | ------------ |
| 例如：管理员 | 可访问所有表         | 完全控制权限 |
|              |                      |              |
|              |                      |              |
| 角色B        |                      |              |
|              |                      |              |
|              |                      |              |

六、数据库的试运行

6.1 填入示例数据

说明：使用insert语句向表中添加必要数据。

6.2 验证数据的完整性

说明：可省略。